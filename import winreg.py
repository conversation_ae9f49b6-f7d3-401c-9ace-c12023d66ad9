import winreg
import os
import datetime

def get_service_creation_time(service_name, service_key):
    """
    Attempts to get service creation/installation time using multiple methods.
    Returns the most reliable timestamp found.
    """

    # Method 1: Check registry key last write time (approximation)
    try:
        info = winreg.QueryInfoKey(service_key)
        # info[8] is the last write time as a Windows FILETIME
        if info[8] != 0:
            # Convert Windows FILETIME to datetime
            # FILETIME is 100-nanosecond intervals since January 1, 1601
            timestamp = info[8] / 10000000.0 - 11644473600  # Convert to Unix timestamp
            return datetime.datetime.fromtimestamp(timestamp)
    except Exception as e:
        print(f"  Error getting registry time: {e}")

    # Method 2: Check executable file creation time if ImagePath exists
    try:
        image_path, _ = winreg.QueryValueEx(service_key, "ImagePath")
        # Clean up the path (remove quotes and parameters)
        if image_path:
            # Remove quotes
            image_path = image_path.strip('"')
            # Take only the executable path (before any parameters)
            if ' ' in image_path and not image_path.startswith('%'):
                image_path = image_path.split(' ')[0]

            # Expand environment variables
            image_path = os.path.expandvars(image_path)

            if os.path.exists(image_path):
                creation_time = os.path.getctime(image_path)
                return datetime.datetime.fromtimestamp(creation_time)
    except Exception as e:
        print(f"  Error getting file time: {e}")

    return None

def get_services_with_creation_times(max_services=100):
    services_path = r"SYSTEM\CurrentControlSet\Services"
    root = winreg.HKEY_LOCAL_MACHINE

    try:
        with winreg.OpenKey(root, services_path) as services_key:
            index = 0
            services = []

            while index < max_services:
                try:
                    service_name = winreg.EnumKey(services_key, index)
                    index += 1

                    # Show progress every 10 services for debugging
                    if index % 10 == 0:
                        print(f"Processing service {index}: {service_name}")

                    # Get creation time using multiple methods
                    try:
                        creation_time = get_service_creation_time(service_name)
                    except Exception as e:
                        print(f"Error getting time for {service_name}: {e}")
                        creation_time = None

                    if creation_time is None:
                        creation_time = "Unknown"

                    services.append((service_name, creation_time))

                except OSError:
                    print(f"Finished reading services at index {index}")
                    break
                except Exception as e:
                    print(f"Error at index {index}: {e}")
                    break

        print(f"Processed {len(services)} services total.")
        return sorted(services, key=lambda x: x[1] if x[1] != "Unknown" else datetime.datetime.max)

    except Exception as e:
        print("Error reading registry:", e)
        return []

if __name__ == "__main__":
    print("Scanning Windows services and their creation times...")
    print("This may take a moment as we check multiple sources for each service.\n")

    services = get_services_with_creation_times()

    print(f"{'Service Name':<50} {'Creation/Install Time':<25} {'Source'}")
    print("-" * 90)

    known_count = 0
    for name, ctime in services:
        if ctime != "Unknown":
            source = "Registry/File"
            known_count += 1
        else:
            source = "N/A"

        ctime_str = ctime.strftime("%Y-%m-%d %H:%M:%S") if ctime != "Unknown" else "Unknown"
        print(f"{name:<50} {ctime_str:<25} {source}")

    print(f"\nSummary: Found creation times for {known_count} out of {len(services)} services.")
