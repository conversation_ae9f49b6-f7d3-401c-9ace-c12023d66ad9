import winreg
import os
import datetime

def get_services_with_creation_times():
    services_path = r"SYSTEM\CurrentControlSet\Services"
    root = winreg.HKEY_LOCAL_MACHINE

    try:
        with winreg.OpenKey(root, services_path) as services_key:
            index = 0
            services = []

            while True:
                try:
                    service_name = winreg.EnumKey(services_key, index)
                    index += 1

                    # Full registry path
                    reg_path = os.path.join(r"SYSTEM\CurrentControlSet\Services", service_name)
                    key = winreg.OpenKey(root, reg_path)
                    info = winreg.QueryInfoKey(key)

                    # QueryInfoKey returns (subkeys, values, last_modified_time)
                    # Creation time must be retrieved via NTFS metadata
                    # So we read the path on disk
                    reg_file = os.path.join(os.environ['SystemRoot'], 'System32', 'config', 'SYSTEM')

                    # NTFS creation time (fallback only if auditing is enabled)
                    try:
                        key_path = fr"C:\Windows\System32\config\SYSTEM"
                        creation_time = os.path.getctime(key_path)
                        creation_time = datetime.datetime.fromtimestamp(creation_time)
                    except:
                        creation_time = "Unknown"

                    services.append((service_name, creation_time))

                except OSError:
                    break

        return sorted(services, key=lambda x: x[1] if x[1] != "Unknown" else datetime.datetime.max)

    except Exception as e:
        print("Error reading registry:", e)
        return []

if __name__ == "__main__":
    services = get_services_with_creation_times()

    print(f"{'Service Name':<60} {'Creation Time'}")
    print("-" * 80)
    for name, ctime in services:
        print(f"{name:<60} {ctime}")
