import winreg
import datetime

print("Starting simple registry test...")

try:
    # Test basic registry access
    services_path = r"SYSTEM\CurrentControlSet\Services"
    root = winreg.HKEY_LOCAL_MACHINE
    
    print("Opening services registry key...")
    with winreg.OpenKey(root, services_path) as services_key:
        print("Successfully opened services key")
        
        # Get first service
        service_name = winreg.EnumKey(services_key, 0)
        print(f"First service: {service_name}")
        
        # Try to get its registry info
        reg_path = f"{services_path}\\{service_name}"
        print(f"Opening service key: {reg_path}")
        
        with winreg.OpenKey(root, reg_path) as key:
            print("Successfully opened service key")
            info = winreg.QueryInfoKey(key)
            print(f"QueryInfoKey returned {len(info)} items")
            print(f"Last write time: {info[8]}")
            
            if info[8] != 0:
                timestamp = info[8] / 10000000.0 - 11644473600
                dt = datetime.datetime.fromtimestamp(timestamp)
                print(f"Converted datetime: {dt}")
            else:
                print("No timestamp available")

except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()

print("Test completed.")
