import winreg
import os
import datetime

def test_service_access():
    """Test basic service registry access"""
    services_path = r"SYSTEM\CurrentControlSet\Services"
    root = winreg.HKEY_LOCAL_MACHINE
    
    print("Testing basic registry access...")
    
    try:
        with winreg.<PERSON><PERSON>ey(root, services_path) as services_key:
            print("Successfully opened services registry key")
            
            # Test reading first few services
            for i in range(5):
                try:
                    service_name = winreg.EnumKey(services_key, i)
                    print(f"Service {i+1}: {service_name}")
                    
                    # Test getting service details
                    reg_path = f"{services_path}\\{service_name}"
                    with winreg.OpenKey(root, reg_path) as key:
                        info = winreg.QueryInfoKey(key)
                        print(f"  Registry info: {len(info)} elements")
                        print(f"  Last write time: {info[8]}")
                        
                        if info[8] != 0:
                            # Convert Windows FILETIME to datetime
                            timestamp = info[8] / 10000000.0 - 11644473600
                            dt = datetime.datetime.fromtimestamp(timestamp)
                            print(f"  Converted time: {dt}")
                        
                except Exception as e:
                    print(f"Error with service {i}: {e}")
                    break
                    
    except Exception as e:
        print(f"Error accessing registry: {e}")

if __name__ == "__main__":
    test_service_access()
